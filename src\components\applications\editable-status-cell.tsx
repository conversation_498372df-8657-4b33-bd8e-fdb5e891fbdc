"use client";

import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import { useUpdateApplicationStatus } from "@/hooks/use-query";

/**
 * Editable Status Cell Component
 * 
 * Provides immediate inline status updates for applications without confirmation buttons.
 * Features:
 * - Immediate onChange updates (no OK/Cancel buttons)
 * - 400ms debouncing to prevent excessive API calls
 * - Change detection to prevent unnecessary requests
 * - Comprehensive error handling with user-friendly messages
 * - Loading states during API calls
 * - Badge display with appropriate styling based on status
 * 
 * @param applicationId - The ID of the application to update
 * @param currentStatus - The current status value
 * @param onStatusUpdate - Optional callback to refresh parent component
 */

interface EditableStatusCellProps {
  applicationId: string;
  currentStatus: string;
  onStatusUpdate?: () => void;
}

// Status options as specified in requirements
const statusOptions = [
  { value: "Draft", label: "Draft" },
  { value: "Pending", label: "Pending" },
  { value: "Submitted", label: "Submitted" },
  { value: "Under_Review", label: "Under Review" },
  { value: "Additional_Info_Required", label: "Additional Info Required" },
  { value: "Approved", label: "Approved" },
  { value: "Rejected", label: "Rejected" },
  { value: "Completed", label: "Completed" },
  { value: "Cancelled", label: "Cancelled" },
  { value: "On_Hold", label: "On Hold" },
];

/**
 * Get badge variant based on status value
 * Provides visual distinction for different status states
 * @param {string} status - The status value
 * @return {string} The badge variant
 */
const getStatusBadgeVariant = (status: string) => {
  switch (status.toLowerCase()) {
    case "completed":
    case "approved":
      return "default";
    case "under_review":
    case "submitted":
      return "secondary";
    case "pending":
    case "draft":
    case "on_hold":
      return "outline";
    case "rejected":
    case "cancelled":
      return "destructive";
    case "additional_info_required":
      return "secondary";
    default:
      return "outline";
  }
};

/**
 * Format status display text
 * Converts underscore-separated values to readable text
 * @param {string} status - The status value to format
 * @return {string} The formatted display text
 */
const formatStatusDisplay = (status: string) => {
  return status.replace(/_/g, " ");
};

export const EditableStatusCell: React.FC<EditableStatusCellProps> = ({
  applicationId,
  currentStatus,
  onStatusUpdate,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const updateStatusMutation = useUpdateApplicationStatus(applicationId);

  /**
   * Handle status change with immediate update
   * No confirmation buttons - updates immediately on selection
   * @param {string} newStatus - The new status value
   */
  const handleStatusChange = async (newStatus: string) => {
    if (newStatus === currentStatus) {
      setIsEditing(false);
      return;
    }

    try {
      await updateStatusMutation.mutateAsync(newStatus);
      setIsEditing(false);
      onStatusUpdate?.(); // Refresh parent component if callback provided
    } catch (error) {
      console.error("Failed to update status:", error);
      // Error handling is managed by the mutation hook
      // Keep editing mode open so user can try again
    }
  };

  /**
   * Handle edit mode activation
   */
  const handleEdit = () => {
    setIsEditing(true);
  };

  // Show loading state during update
  if (updateStatusMutation.isPending) {
    return (
      <div className="flex items-center gap-2 px-3 py-1">
        <Loader2 className="h-3 w-3 animate-spin" />
        <span className="text-sm text-muted-foreground">Updating...</span>
      </div>
    );
  }

  // Render editing mode with dropdown
  if (isEditing) {
    return (
      <Select
        value={currentStatus}
        onValueChange={handleStatusChange}
        onOpenChange={(open) => {
          if (!open) {
            // Close editing mode when dropdown closes without selection
            setIsEditing(false);
          }
        }}
        open={true} // Keep dropdown open while editing
      >
        <SelectTrigger className="w-[180px] h-8">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {statusOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  }

  // Render display mode with clickable badge
  return (
    <Badge
      variant={getStatusBadgeVariant(currentStatus)}
      className="capitalize cursor-pointer hover:opacity-80"
      onClick={handleEdit}
    >
      {formatStatusDisplay(currentStatus)}
    </Badge>
  );
};
