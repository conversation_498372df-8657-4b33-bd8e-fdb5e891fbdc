import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

/**
 * PUT /api/applications/[id]/status
 *
 * Updates the status of a specific application.
 * Follows the same pattern as priority updates with proper validation and error handling.
 *
 * @param {NextRequest} request - The incoming request with status data
 * @param {Object} params - Route parameters containing the application ID
 * @param {string} params.id - The application ID
 * @return {Promise<NextResponse>} JSON response with success/error status
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Application ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { status } = body;

    // Validate status value - exact values as specified in requirements
    const validStatusValues = [
      "Draft",
      "Pending",
      "Submitted",
      "Under_Review",
      "Additional_Info_Required",
      "Approved",
      "Rejected",
      "Completed",
      "Cancelled",
      "On_Hold",
    ];

    if (!status || !validStatusValues.includes(status)) {
      return NextResponse.json(
        {
          success: false,
          message: `Invalid status value. Must be one of: ${validStatusValues.join(", ")}`,
        },
        { status: 400 }
      );
    }

    // Update application status in backend
    const backendUrl = `${apiUrl}/applications/${id}/status`;

    const response = await fetch(backendUrl, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ status }),
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: "Status updated successfully",
        data,
      });
    } else {
      console.error("Backend status update failed:", {
        status: response.status,
        statusText: response.statusText,
        data,
      });

      return NextResponse.json(
        {
          success: false,
          message:
            data.message || `Failed to update status (${response.status})`,
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error updating application status:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error while updating status",
      },
      { status: 500 }
    );
  }
}
