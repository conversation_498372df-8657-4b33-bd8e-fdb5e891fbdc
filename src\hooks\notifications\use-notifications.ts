"use client";

import { useRef, useCallback } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import { useAuthError<PERSON>and<PERSON> } from "@/hooks/use-auth-error-handler";

// Toast configurations
const toastStyles = {
  success: {
    style: {
      background: "#10B981",
      color: "#FFFFFF",
      border: "none",
    },
  },
  error: {
    style: {
      background: "#EF4444",
      color: "#FFFFFF",
      border: "none",
    },
  },
};

/**
 * Custom hook for fetching notification settings
 * Implements proper loading states, error handling, and authentication
 * @return {Object} React Query object with notification settings data
 */
export const useNotificationSettings = () => {
  const { data: session, status } = useSession();
  const { handle401Error } = useAuthErrorHandler();

  return useQuery({
    queryKey: ["notification-settings"],
    queryFn: async (): Promise<INotificationSettings> => {
      const response = await fetch("/api/notifications/settings", {
        headers: {
          "Content-Type": "application/json",
        },
        cache: "no-store",
      });

      if (!response.ok) {
        if (response.status === 401) {
          handle401Error();
          throw new Error("Authentication required");
        }

        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || "Failed to fetch notification settings"
        );
      }

      const result = await response.json();
      return result.data;
    },
    enabled:
      status === "authenticated" && !!session?.backendTokens?.accessToken,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error.message === "Authentication required") {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Custom hook for updating notification settings
 * Implements debouncing, change detection, and optimistic updates
 * @return {Object} React Query mutation object for updating settings
 */
export const useUpdateNotificationSettings = () => {
  const queryClient = useQueryClient();
  const lastDataRef = useRef<INotificationSettings | null>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Deep comparison function for change detection
  const hasChanges = useCallback((newData: INotificationSettings): boolean => {
    if (!lastDataRef.current) return true;
    return JSON.stringify(lastDataRef.current) !== JSON.stringify(newData);
  }, []);

  return useMutation({
    mutationFn: async (
      data: INotificationSettings
    ): Promise<INotificationSettings> => {
      // Change detection - only proceed if data has actually changed
      if (!hasChanges(data)) {
        return data; // Return current data without API call
      }

      // Clear existing debounce timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Return a promise that resolves after debounce delay
      return new Promise((resolve, reject) => {
        debounceTimeoutRef.current = setTimeout(async () => {
          try {
            const response = await fetch("/api/notifications/settings", {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(data),
            });

            if (!response.ok) {
              const errorData = await response.json().catch(() => ({}));
              throw new Error(
                errorData.message || "Failed to update notification settings"
              );
            }

            const result = await response.json();
            lastDataRef.current = data; // Update reference on successful save
            resolve(result.data);
          } catch (error) {
            reject(error);
          }
        }, 500); // 500ms debounce delay
      });
    },
    onSuccess: (data, variables) => {
      // Only show success toast if there were actual changes
      if (hasChanges(variables)) {
        toast.success("Settings updated successfully", {
          description: "Your notification preferences have been saved",
          ...toastStyles.success,
        });
      }

      // Update query cache
      queryClient.setQueryData(["notification-settings"], data);
    },
    onError: (error: any) => {
      let errorMessage = "Failed to update notification settings";
      let errorDescription =
        "Please try again or contact support if the issue persists.";

      if (error.message) {
        if (
          error.message.includes("timeout") ||
          error.message.includes("network")
        ) {
          errorMessage = "Network error";
          errorDescription =
            "Please check your internet connection and try again.";
        } else if (error.message.includes("Authentication")) {
          errorMessage = "Session expired";
          errorDescription = "Please refresh the page and log in again.";
        } else {
          errorMessage = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        ...toastStyles.error,
      });
    },
    // Cleanup function
    onSettled: () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
        debounceTimeoutRef.current = null;
      }
    },
  });
};

/**
 * Cleanup hook for notification-related timeouts and references
 * Should be called on component unmount
 * @return {Function} Cleanup function to clear timeouts and references
 */
export const useNotificationCleanup = () => {
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
      debounceTimeoutRef.current = null;
    }
  }, []);
};
