import { z } from "zod";

export const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1, "Password can't be empty"),
});

export const signupSchema = z
  .object({
    name: z.string().min(1, "Name can't be empty"),
    email: z.string().email(),
    password: z.string().min(8).max(20),
    confirmPassword: z.string().min(8).max(20),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirm"],
  });

export const userSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  email: z.string().email(),
  password: z.string().optional(),
});

export const mentorSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  image: z.string(),
  order: z.coerce.number().optional(),
  desc: z.string().min(1, "Desc can't be empty"),
  designation: z.string().min(1, "Designation can't be empty"),
  email: z.string().email(),
  password: z.string().optional(),
  linkedin: z.string().optional(),
  profile: z.string().optional(),
});
export const serviceSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  description: z.string().min(1, "Desc can't be empty"),
  price: z.coerce.number().min(1, "Price can't be empty"),
  meeting_link: z.string().url(),
});

export const blogSchema = z.object({
  title: z.string(),
  summary: z.string(),
  img: z.string(),
  blogger: z.string(),
  slug: z.string().optional(),
  desc: z.string(),
});

export const packageSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  note: z.string().min(1, "Note can't be empty"),
  amount: z.coerce.number().min(1, "Amount can't be empty"),
  order: z.coerce.number().optional(),
  service: z.array(z.string()),
});

export const immigrationSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  amount: z.coerce.number().min(1, "Amount can't be empty"),
  order: z.coerce.number().optional(),
  service: z.array(z.string()),
  website_visible: z.boolean().default(false),
});
export const trainingSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  img: z.string(),
  order: z.coerce.number().optional(),
  amount: z.coerce.number().min(1, "Amount can't be empty"),
  service: z.array(z.string()),
  highlights: z.array(z.string()),
});
export const customerReviewSchema = z.object({
  name: z.string().min(1, "Name can't be empty"),
  img: z.string().optional(),
  source: z.string(),
  comment: z.string(),
  date: z.date(),
  order: z.coerce.number().optional(),
  rating: z.coerce.number(),
});

// Document Master Schema
export const documentMasterSchema = z.object({
  name: z.string().min(1, "Document name is required"),
  description: z.string().optional(),
  category: z.string().min(1, "Category is required"),
  instructions: z.string().optional(),
});

// Legacy schema for backward compatibility
export const immigrationDocumentSchema = z.object({
  name: z.string().min(1, "Document name is required"),
  description: z.string().optional(),
  category: z.string().min(1, "Category is required"),
  document_type: z.object({
    id: z.string().min(1, "Document type is required"),
    name: z.string(),
    description: z.string().optional(),
  }),
  instructions: z.string().optional(),
});

// Workflow Master Schema
export const workflowMasterSchema = z.object({
  name: z.string().min(1, "Workflow name is required"),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
});

// Notification Template Schema
export const notificationTemplateSchema = z.object({
  name: z.string().min(1, "Template name is required"),
  description: z.string().optional(),
  type: z.enum([
    "document_reminder",
    "application_submitted",
    "agent_assigned",
    "status_update",
    "agent_query",
    "document_rejected",
    "authority_query",
    "deadline_warning",
    "missing_document",
    "eligibility_confirmation",
    "payment_confirmation",
    "final_decision",
    "system_maintenance",
    "escalation_notice",
  ]),
  subject: z.string().min(1, "Subject is required"),
  content: z.string().min(1, "Content is required"),
  channels: z
    .array(z.enum(["email", "sms", "in_app"]))
    .min(1, "At least one channel is required"),
  isActive: z.boolean().default(true),
  variables: z.array(z.string()).optional(),
});

// Notification Settings Schema - Clean implementation
export const notificationSettingsSchema = z.object({
  agent_assigned: z.boolean(),
  case_status_update: z.boolean(),
  agent_query: z.boolean(),
  document_rejection: z.boolean(),
  missing_document_reminder_days: z
    .number()
    .min(1, "Must be at least 1 day")
    .max(365, "Cannot exceed 365 days"),
  system_maintenance: z.boolean(),
  upcoming_deadline_alerts: z.boolean(),
  final_decision_issued: z.boolean(),
});

// Workflow Template Schema
export const workflowFormFieldSchema = z.object({
  fieldName: z.string().min(1, "Field name is required"),
  fieldType: z.string().min(1, "Field type is required"),
  required: z.boolean().default(false),
  options: z.array(z.string()).optional(), // For select and checkbox field types
  showToClient: z.boolean().default(true), // Controls visibility on client-facing forms
});

export const workflowDocumentSchema = z.object({
  documentName: z.string().min(1, "Document name is required"),
  required: z.boolean().default(false),
});

export const workflowStageSchema = z.object({
  stageName: z.string().min(1, "Stage name is required"),
  stageOrder: z.number().min(1, "Stage order must be at least 1"),
  documentsRequired: z.boolean().default(false),
  documents: z.array(workflowDocumentSchema).default([]),
  customFormRequired: z.boolean().default(false),
  customForm: z.array(workflowFormFieldSchema).default([]),
  showToClient: z.boolean().default(true), // Controls stage visibility on client-facing forms
});

export const workflowTemplateSchema = z.object({
  name: z.string().min(1, "Template name is required"),
  description: z.string().optional(),
  serviceType: z.string().optional(),
  serviceId: z.string().optional(),
  immigrationPackageId: z.string().optional(),
  packageName: z.string().optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().optional(),
  workflowTemplate: z
    .array(workflowStageSchema)
    .min(1, "At least one stage is required"),
});

export const createWorkflowTemplateSchema = z.object({
  name: z.string().min(1, "Template name is required"),
  description: z.string().optional(),
  serviceType: z.string().optional(),
  serviceId: z.string().optional(),
  immigrationPackageId: z.string().min(1, "Immigration package is required"),
  packageName: z.string().optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().optional(),
  workflowTemplate: z
    .array(workflowStageSchema)
    .min(1, "At least one stage is required"),
});

// Agent Schema
export const agentSchema = z.object({
  name: z.string().min(1, "Agent name is required"),
  email: z.string().email("Valid email is required"),
  phone: z.string().min(1, "Phone number is required"),
  status: z.enum(["Active", "Inactive", "Blocked"]).default("Active"),
});

// Agent Login Schema
export const agentLoginSchema = z.object({
  email: z.string().email("Valid email is required"),
  password: z.string().min(1, "Password can't be empty"),
});

// Agent Password Reset Schema
export const agentPasswordResetSchema = z.object({
  email: z.string().email("Valid email is required"),
});

// Agent Password Reset Confirm Schema
export const agentPasswordResetConfirmSchema = z.object({
  token: z.string().min(1, "Reset token is required"),
  newPassword: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .max(20),
});

// Create New Application Schemas
export const createUserSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Valid email is required"),
  phone: z.string().min(1, "Phone number is required"),
});

export const createImmigrationProductSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  description: z.string().optional(),
  price: z.coerce.number().min(0, "Price must be a positive number"),
  category: z.string().min(1, "Category is required"),
});

export const discountApplicationSchema = z
  .object({
    originalPrice: z.coerce.number().min(0, "Original price must be positive"),
    discountAmount: z.coerce
      .number()
      .min(0, "Discount must be positive")
      .optional(),
    discountedPrice: z.coerce.number().min(0, "Final price must be positive"),
  })
  .refine(
    (data) => {
      if (data.discountAmount && data.discountAmount > data.originalPrice) {
        return false;
      }
      return true;
    },
    {
      message: "Discount cannot exceed original price",
      path: ["discountAmount"],
    }
  );

export const createApplicationSchema = z
  .object({
    // User Selection/Creation
    userId: z.string().optional(),
    createNewUser: z.boolean().default(false),
    newUser: createUserSchema.optional(),

    // Immigration Product Selection/Creation
    immigrationProductId: z.string().optional(),
    createNewProduct: z.boolean().default(false),
    newProduct: createImmigrationProductSchema.optional(),

    // Discount Application
    originalPrice: z.coerce.number().min(0, "Original price must be positive"),
    discountAmount: z.coerce
      .number()
      .min(0, "Discount must be positive")
      .optional(),
    discountedPrice: z.coerce.number().min(0, "Final price must be positive"),

    // Workflow Template Selection/Creation
    workflowTemplateId: z.string().min(1, "Workflow template is required"),
    createNewTemplate: z.boolean().default(false),

    // Agent Assignment (Optional)
    assignedAgentId: z.string().optional(),

    // Application Metadata
    priorityLevel: z.enum(["Low", "Medium", "High"]).default("Medium"),
    notes: z.string().optional(),
  })
  .refine(
    (data) => {
      // Validate user selection
      if (!data.createNewUser && !data.userId) {
        return false;
      }
      if (data.createNewUser && !data.newUser) {
        return false;
      }
      return true;
    },
    {
      message: "Either select an existing user or provide new user details",
      path: ["userId"],
    }
  )
  .refine(
    (data) => {
      // Validate product selection
      if (!data.createNewProduct && !data.immigrationProductId) {
        return false;
      }
      if (data.createNewProduct && !data.newProduct) {
        return false;
      }
      return true;
    },
    {
      message:
        "Either select an existing product or provide new product details",
      path: ["immigrationProductId"],
    }
  )
  .refine(
    (data) => {
      // Validate discount logic
      if (data.discountAmount && data.discountAmount > data.originalPrice) {
        return false;
      }
      return true;
    },
    {
      message: "Discount cannot exceed original price",
      path: ["discountAmount"],
    }
  );

// New Application API Schema for multiple payments and agents
export const createApplicationApiSchema = z.object({
  service_type: z.string().min(1, "Service type is required"),
  service_id: z.string().min(1, "Service ID is required"),
  user_id: z.string().min(1, "User ID is required"),
  priority_level: z.enum(["Low", "Medium", "High"]).default("Medium"),
  workflow_template_id: z.string().min(1, "Workflow template ID is required"),
  payments: z
    .array(z.string().min(1, "Payment ID cannot be empty"))
    .min(1, "At least one payment ID is required"),
  assigned_agent: z
    .array(z.string().min(1, "Agent ID cannot be empty"))
    .optional(),
});

export type CreateApplicationApiRequest = z.infer<
  typeof createApplicationApiSchema
>;

/**
 * Payment Creation Schema (v2 API)
 *
 * Updated schema to match the new API payload structure:
 * - Uses 'serviceType' and 'serviceId' instead of 'service_type' and 'immigration_service_id'
 * - Uses 'transactionId' instead of 'transaction_id'
 * - Supports multiple payment methods: stripe, cash, bank_deposit, online_transfer
 * - transactionId is optional for Stripe payments, required for other methods
 */
export const createPaymentSchema = z.object({
  amount: z.number().min(1, "Amount must be greater than 0"),
  user_id: z.string().min(1, "User ID is required"),
  serviceType: z.enum(["immigration"], {
    required_error: "Service type is required",
  }),
  serviceId: z.string().min(1, "Service ID is required"),
  discount_amount: z
    .number()
    .min(0, "Discount amount must be non-negative")
    .default(0), // Always default to 0 instead of optional
  actual_amount: z.number().min(1, "Actual amount must be greater than 0"),
  payment_method: z.enum(
    ["stripe", "cash", "bank_deposit", "online_transfer"],
    {
      required_error: "Payment method is required",
    }
  ),
  transactionId: z.string().optional(), // Optional for all payment methods
});

export type CreatePaymentRequest = z.infer<typeof createPaymentSchema>;
