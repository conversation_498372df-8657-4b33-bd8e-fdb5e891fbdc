"use client";

/**
 * Editable Workflow Template Cell Component
 *
 * This component provides a compact, inline editing interface for workflow templates
 * following the same design pattern as the priority update section. It features:
 *
 * - Badge display for current workflow template with click-to-edit functionality
 * - Inline dropdown selector with confirmation buttons
 * - Dynamic service ID filtering for relevant templates
 * - Comprehensive error handling and user feedback
 * - Memory leak prevention and proper cleanup
 * - User-friendly loading and empty states
 *
 * @version 1.0.0
 * <AUTHOR> System Enhancement Team
 */

import React, { useState, useEffect, useCallback } from "react";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Check, X, Loader2, Workflow, AlertTriangle } from "lucide-react";
import { toast } from "sonner";
import { useSession } from "next-auth/react";
import { apiUrl } from "@/utils/urls";

/**
 * Workflow Template Interface
 * Represents a workflow template with its basic properties
 */
interface WorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  serviceType?: string;
  serviceId?: string;
}

/**
 * Props for EditableWorkflowTemplateCell Component
 *
 * @param applicationId - The ID of the application being modified
 * @param serviceId - Dynamic service ID from application data for filtering templates
 * @param currentWorkflowTemplateId - ID of the currently assigned workflow template
 * @param currentWorkflowTemplateName - Name of the currently assigned workflow template
 * @param onWorkflowTemplateUpdate - Callback function to refresh parent component after successful update
 */
interface EditableWorkflowTemplateCellProps {
  applicationId: string;
  serviceId?: string;
  currentWorkflowTemplateId?: string;
  currentWorkflowTemplateName?: string;
  onWorkflowTemplateUpdate?: () => void;
}

/**
 * Get badge variant based on workflow template status
 * Provides visual distinction for different template states
 *
 * @param {string} templateName - The name of the workflow template
 * @return {string} The badge variant to use for styling
 */
const getWorkflowTemplateBadgeVariant = (templateName?: string) => {
  if (!templateName) return "outline";
  return "secondary";
};

/**
 * Editable Workflow Template Cell Component
 *
 * Provides a compact interface for changing workflow templates with:
 * - Inline editing similar to priority cell design
 * - Dynamic service ID filtering
 * - User confirmation for changes
 * - Comprehensive error handling
 *
 * @param {EditableWorkflowTemplateCellProps} props - The component props
 * @return {JSX.Element} The rendered editable workflow template cell component
 */
export const EditableWorkflowTemplateCell: React.FC<
  EditableWorkflowTemplateCellProps
> = ({
  applicationId,
  serviceId,
  currentWorkflowTemplateId,
  currentWorkflowTemplateName,
  onWorkflowTemplateUpdate,
}) => {
  const { data: session } = useSession();

  // Component state management
  const [isEditing, setIsEditing] = useState(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState(
    currentWorkflowTemplateId || ""
  );
  const [isUpdating, setIsUpdating] = useState(false);
  const [workflowTemplates, setWorkflowTemplates] = useState<
    WorkflowTemplate[]
  >([]);
  const [loading, setLoading] = useState(false);

  /**
   * Fetch workflow templates filtered by service ID
   * Uses dynamic service ID extraction from application data
   */
  const fetchWorkflowTemplates = useCallback(async () => {
    if (!session?.backendTokens?.accessToken || !isEditing) return;

    setLoading(true);
    try {
      // Build API URL with dynamic service ID or fallback to immigration default
      const apiServiceId =
        serviceId || "cmcp500ap0000istocjrscl50p0000istocjrscl50"; // Fallback for backward compatibility
      const response = await fetch(
        `${apiUrl}/workflow-templates?serviceId=${apiServiceId}&limit=100`,
        {
          headers: {
            Authorization: `Bearer ${session.backendTokens.accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setWorkflowTemplates(data.data || []);
      } else {
        console.error("Failed to fetch workflow templates");
        toast.error("Failed to load workflow templates", {
          description: "Please try again later",
        });
      }
    } catch (error) {
      console.error("Error fetching workflow templates:", error);
      toast.error("Failed to load workflow templates", {
        description: "Please check your connection and try again",
      });
    } finally {
      setLoading(false);
    }
  }, [session, serviceId, isEditing]);

  // Fetch templates when editing starts
  useEffect(() => {
    if (isEditing) {
      fetchWorkflowTemplates();
    }
  }, [isEditing, fetchWorkflowTemplates]);

  /**
   * Handle edit mode activation
   * Initializes editing state and fetches available templates
   */
  const handleEdit = () => {
    setIsEditing(true);
    setSelectedTemplateId(currentWorkflowTemplateId || "");
  };

  /**
   * Handle edit cancellation
   * Resets state and exits editing mode
   */
  const handleCancel = () => {
    setIsEditing(false);
    setSelectedTemplateId(currentWorkflowTemplateId || "");
    setWorkflowTemplates([]); // Clear templates to free memory
  };

  /**
   * Handle workflow template save with confirmation
   * Implements the POST /applications/assign-workflow-template API integration
   */
  const handleSave = async () => {
    if (selectedTemplateId === currentWorkflowTemplateId) {
      setIsEditing(false);
      return;
    }

    if (!session?.backendTokens?.accessToken) {
      toast.error("Authentication required", {
        description: "Please sign in again to continue",
      });
      return;
    }

    // Find the selected template name for user feedback
    const selectedTemplate = workflowTemplates.find(
      (t) => t.id === selectedTemplateId
    );
    const templateName = selectedTemplate?.name || "Unknown Template";

    setIsUpdating(true);
    try {
      // Use the POST /api/applications/assign-workflow-template endpoint
      const response = await fetch(
        `/api/applications/assign-workflow-template`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${session.backendTokens.accessToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            application_id: applicationId,
            new_workflow_template_id: selectedTemplateId,
          }),
        }
      );

      if (response.ok) {
        setIsEditing(false);
        setWorkflowTemplates([]); // Clear templates to free memory
        toast.success("Workflow template updated successfully", {
          description: `Changed to "${templateName}"`,
        });
        onWorkflowTemplateUpdate?.();
      } else {
        const errorData = await response.json();
        toast.error("Failed to update workflow template", {
          description: errorData.message || "Please try again later",
        });
        setSelectedTemplateId(currentWorkflowTemplateId || ""); // Reset to original value
      }
    } catch (error) {
      console.error("Error updating workflow template:", error);

      // Provide user-friendly error message
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to update workflow template";

      toast.error(errorMessage, {
        description: "Please check your connection and try again",
      });
      setSelectedTemplateId(currentWorkflowTemplateId || ""); // Reset to original value
    } finally {
      setIsUpdating(false);
    }
  };

  // Show loading state when fetching templates
  if (loading && isEditing) {
    return (
      <div className="flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground bg-muted/50 rounded-md border border-dashed">
        <Loader2 className="h-3 w-3 animate-spin" />
        <span>Loading templates...</span>
      </div>
    );
  }

  // Render editing mode with dropdown and action buttons
  if (isEditing) {
    return (
      <div className="flex items-center gap-2">
        <Select
          value={selectedTemplateId}
          onValueChange={setSelectedTemplateId}
        >
          <SelectTrigger className="w-[200px] h-8">
            <SelectValue placeholder="Select template..." />
          </SelectTrigger>
          <SelectContent className="max-w-[300px]">
            {workflowTemplates.length === 0 ? (
              <div className="flex items-center gap-2 p-4 text-sm text-muted-foreground">
                <AlertTriangle className="h-4 w-4" />
                <span>No workflow templates available for this service</span>
              </div>
            ) : (
              workflowTemplates.map((template) => (
                <SelectItem
                  key={template.id}
                  value={template.id}
                  className="cursor-pointer"
                >
                  <div className="flex flex-col gap-1 py-1">
                    <div className="font-medium text-sm">{template.name}</div>
                    {template.description && (
                      <div className="text-xs text-muted-foreground line-clamp-2 max-w-[250px]">
                        {template.description}
                      </div>
                    )}
                  </div>
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
        <div className="flex gap-1">
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={handleSave}
            disabled={isUpdating || workflowTemplates.length === 0}
          >
            {isUpdating ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Check className="h-3 w-3" />
            )}
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={handleCancel}
            disabled={isUpdating}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  }

  // Render display mode with clickable badge
  return (
    <div className="flex items-center gap-2">
      <Workflow className="h-3 w-3 text-muted-foreground" />
      <Badge
        variant={getWorkflowTemplateBadgeVariant(currentWorkflowTemplateName)}
        className="capitalize cursor-pointer hover:opacity-80 max-w-[200px] truncate"
        onClick={handleEdit}
        title={currentWorkflowTemplateName || "No template assigned"}
      >
        {currentWorkflowTemplateName || "No template"}
      </Badge>
    </div>
  );
};
