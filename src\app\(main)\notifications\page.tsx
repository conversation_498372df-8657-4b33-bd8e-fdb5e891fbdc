"use client";

import React, { useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, Refresh<PERSON>w, Settings } from "lucide-react";
import { NotificationSettings } from "@/components/notifications/notification-settings";
import {
  useNotificationSettings,
  useNotificationCleanup,
} from "@/hooks/notifications/use-notifications";
import { useAuthErrorHandler } from "@/hooks/use-auth-error-handler";

const NotificationsPage: React.FC = () => {
  const { status } = useSession();
  const { handle401Error } = useAuthErrorHandler();
  const cleanup = useNotificationCleanup();

  // Fetch notification settings
  const {
    data: settings,
    isLoading,
    error,
    refetch,
    isRefetching,
  } = useNotificationSettings();

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // Handle retry
  const handleRetry = () => {
    refetch();
  };

  // Loading state for session
  if (status === "loading") {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  // Unauthenticated state
  if (status === "unauthenticated") {
    handle401Error();
    return null;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Settings className="h-8 w-8" />
            Notification Settings
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage your notification preferences and stay updated on your
            applications
          </p>
        </div>
        {settings && (
          <Button
            variant="outline"
            onClick={handleRetry}
            disabled={isRefetching}
            className="flex items-center gap-2"
          >
            <RefreshCw
              className={`h-4 w-4 ${isRefetching ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        )}
      </div>

      {/* Main content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Notification Preferences
          </CardTitle>
        </CardHeader>
        <CardContent>
          {error ? (
            // Error state
            <div className="text-center py-12 space-y-6">
              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <AlertCircle className="h-8 w-8 text-red-600" />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-gray-900">
                  Unable to load notification settings
                </h3>
                <p className="text-sm text-gray-600 max-w-md mx-auto">
                  {error instanceof Error
                    ? error.message.includes("timeout") ||
                      error.message.includes("network")
                      ? "The request took too long to complete. Please check your internet connection and try again."
                      : error.message
                    : "An unexpected error occurred while loading your notification preferences."}
                </p>
              </div>
              <div className="flex gap-3 justify-center">
                <Button
                  onClick={handleRetry}
                  disabled={isRefetching}
                  className="flex items-center gap-2"
                >
                  {isRefetching ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Retrying...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4" />
                      Try Again
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Refresh Page
                </Button>
              </div>
            </div>
          ) : settings ? (
            // Settings loaded successfully
            <NotificationSettings settings={settings} isLoading={isLoading} />
          ) : (
            // Loading state
            <div className="space-y-6">
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">
                  Loading notification settings...
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationsPage;
