"use client";

import React, { useState, useCallback, useRef, useEffect } from "react";
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { FilterSelect } from "@/components/ui/filter-select";
import { DataTablePagination } from "@/components/ui/data-table-pagination";
import { FileText, Search, RefreshCw, ChevronDown } from "lucide-react";
import { createApplicationsColumns } from "./applications-columns";
import { AssignAgentDialog } from "./assign-agent-dialog";

interface ApplicationsDataTableProps {
  data: IApplication[];
  loading: boolean;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  onPageChange: (page: number) => void;
  onSearch: (search: string) => void;
  onStatusFilter: (status: string) => void;
  onPriorityLevelFilter: (priorityLevel: string) => void;
  onRefresh?: () => void; // Optional callback to refresh data after updates
  userRole?: "user" | "admin" | "agent"; // User role for conditional rendering
}

export const ApplicationsDataTable: React.FC<ApplicationsDataTableProps> = ({
  data,
  loading,
  pagination,
  onPageChange,
  onSearch,
  onStatusFilter,
  onPriorityLevelFilter,
  onRefresh,
  userRole,
}) => {
  const [searchValue, setSearchValue] = useState("");
  const [statusFilter, setStatusFilterValue] = useState("all");
  const [priorityLevelFilter, setPriorityLevelFilterValue] = useState("all");
  const [assigningApplication, setAssigningApplication] =
    useState<IApplication | null>(null);

  // TanStack Table state
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    application_number: false, // Hide Application Number by default
  });
  const [globalFilter, setGlobalFilter] = useState("");

  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Priority update handler
  const handlePriorityUpdate = useCallback(
    async (_applicationId: string, _newPriority: string) => {
      onRefresh?.();
    },
    [onRefresh]
  );

  // Status update handler
  const handleStatusUpdate = useCallback(() => {
    onRefresh?.();
  }, [onRefresh]);

  // Create columns with handlers
  const columns = React.useMemo(
    () =>
      createApplicationsColumns(
        handlePriorityUpdate,
        setAssigningApplication,
        userRole,
        onRefresh, // Pass refresh callback for workflow template updates
        handleStatusUpdate // Pass refresh callback for status updates
      ),
    [handlePriorityUpdate, userRole, onRefresh, handleStatusUpdate]
  );

  // Create table
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: "includesString",
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      globalFilter,
    },
  });

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const handleSearchChange = useCallback(
    (value: string) => {
      setSearchValue(value);

      // Clear existing timeout
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      // Debounce search by 300ms
      searchTimeoutRef.current = setTimeout(() => {
        onSearch(value);
      }, 300);
    },
    [onSearch]
  );

  const handleStatusChange = (value: string) => {
    setStatusFilterValue(value);
    onStatusFilter(value === "all" ? "" : value);
  };

  const handlePriorityLevelChange = (value: string) => {
    setPriorityLevelFilterValue(value);
    onPriorityLevelFilter(value === "all" ? "" : value);
  };

  // Sync search with global filter
  useEffect(() => {
    setGlobalFilter(searchValue);
  }, [searchValue]);

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center space-x-2 flex-wrap gap-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search applications..."
              value={searchValue}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-8 max-w-sm"
            />
          </div>
          <FilterSelect
            value={statusFilter}
            onValueChange={handleStatusChange}
            options={[
              { value: "all", label: "All Status" },
              { value: "pending", label: "Pending" },
              { value: "active", label: "Active" },
              { value: "completed", label: "Completed" },
              { value: "rejected", label: "Rejected" },
              { value: "cancelled", label: "Cancelled" },
            ]}
            placeholder="Status"
            className="w-[150px]"
          />
          <FilterSelect
            value={priorityLevelFilter}
            onValueChange={handlePriorityLevelChange}
            options={[
              { value: "all", label: "All Priority" },
              { value: "High", label: "High" },
              { value: "Medium", label: "Medium" },
              { value: "Low", label: "Low" },
              { value: "Critical", label: "Critical" },
            ]}
            placeholder="Priority"
            className="w-[150px]"
          />
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={loading}
            >
              <RefreshCw
                className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
              />
            </Button>
          )}
        </div>

        {/* Column Visibility */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
              Columns <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id.replace(/_/g, " ")}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Applications Table */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading applications...</p>
          </div>
        </div>
      ) : data.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-lg font-medium">No applications found</p>
          <p className="text-muted-foreground">
            Try adjusting your search or filters
          </p>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Pagination */}
      <DataTablePagination
        currentPage={pagination.page}
        totalPages={pagination.totalPages}
        totalItems={pagination.total}
        itemsPerPage={pagination.limit}
        onPageChange={onPageChange}
      />

      {/* Assign Agent Dialog */}
      {assigningApplication && (
        <AssignAgentDialog
          application={assigningApplication}
          open={!!assigningApplication}
          onOpenChange={(open) => !open && setAssigningApplication(null)}
          onSuccess={() => {
            setAssigningApplication(null);
            onRefresh?.();
          }}
        />
      )}
    </div>
  );
};
